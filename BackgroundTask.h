#pragma once
#include <vector>
#include <thread>
#include <functional>
#include <mutex>
#include <iostream>
#include <shared_mutex>
#include <queue>
using namespace std;

class BackgroundTask
{
public:
    vector<int> &arr; // 引用成员变量
    BackgroundTask(vector<int> &a) : arr(a) {};
    void operator()(vector<int> &a);

    void do_lengthy_work(int a);
};

class ThreadGuard
{
    thread &t;

public:
    explicit ThreadGuard(thread &t_) : t(t_) {};
    ~ThreadGuard()
    {
        if (t.joinable())
        {
            t.join();
        }
    }
    ThreadGuard(ThreadGuard const &) = delete;
    ThreadGuard &operator=(ThreadGuard const &) = delete;
};

/**
 * 用于初始化的，只锁一次的锁例子
 */
class OnceInitDemo
{
    vector<int> connection_data;
    // once_flag 同步原语，用于确保某个函数或代码块在多线程环境中只被执行一次。
    std::once_flag onceFlag_connection;
    void connection_open(const vector<int> &v)
    {
        cout << "\n模拟打开通道\n";
        connection_data = v;
    }

public:
    void orderSend(const vector<int> &v, char c)
    {
        /* 用call_once只锁一次
           例如每次发送数据，要检查是否打开了通讯通道，检查的时候保证数据不变量，需要上锁。
           如果用普通的锁，线程到此处就卡成序列化了
         */
        call_once(onceFlag_connection, &OnceInitDemo::connection_open, this, std::ref(v));
        cout << "\n模拟发送数据：" << c << endl;
        for (auto i : v)
        {
            cout << c << i << " ";
        }
        cout << endl;
    }
};

/**
 * 写锁，读不锁
 */
class Dns_cache
{
    // shared_mutex 共享锁，用shared_lock上锁时，线程可通过；用lock_guard锁时，就锁上。
    // 还有一种：shared_timed_mutex，可以等锁一端时间。例如等多久别人没算出来，就自己算。
    mutable shared_mutex mutex_dns; // 加mutable，表示const函数可以修改。

    vector<int> data;

public:
    Dns_cache(vector<int> &v) : data(v) {};
    void reader(const char &c)
    {
        // 读锁，可以多个线程同时读。
        cout << "\n读锁，关键字" << c << "：\n";
        shared_lock<shared_mutex> lk(mutex_dns);
        for (auto i : data)
        {
            cout << c << i << " ";
        }
    }
    void writer()
    {
        // lock_guard 上锁后，shared_lock<shared_mutex> lk(mutex_dns)这些门都上锁了。
        cout << "\n写锁：\n";
        lock_guard<shared_mutex> lk(mutex_dns);
        for (auto &i : data)
        {
            i = i * 10;
            cout << i << " ";
        }
    }
};

template <typename T>
class ThreadSafe_queue
{
    std::queue<T> queueData;
    // 引用下面的函数 ThreadSafe_queue(ThreadSafe_queue const& other_q ) 中写了const，代表不修改里面的值，
    // 但mutex又必须要修改，所以要用mutable申明，取得可修改的豁免
    mutable std::mutex mutex_queue;
    std::condition_variable cv; // 条件变量，用于通知。

public:
    ThreadSafe_queue() {};
    // const 表示不修改other_q的值，& 代表引用，避免拷贝。
    ThreadSafe_queue(ThreadSafe_queue const &other_q)
    {
        std::lock_guard<mutex> lk(mutex_queue);
        queueData = other_q.queueData; // 复制队列数据
    }
    void push(T t)
    {
        // lock_guard 比 unique_lock 更高效
        std::lock_guard<mutex> lk(mutex_queue);
        queueData.push(t);
        cv.notify_one(); // 通知一个wait状态的线程
    };
    /*   bool isEmpty()
      {
          return queueData.empty();
      }; */
    std::shared_ptr<T> wait_and_pop()
    {
        // 先锁上。用unique_lock而不用lock_guard，是因为unique_lock可以开开关关。
        std::unique_lock<mutex> lk(mutex_queue);
        // 如果wait满足条码，继续下一步。
        // 如果wait不满足，就开锁，然后等在这个位置，直到唤醒，再判断满不满足...
        // [this]，这里可以不写this，但如果在cpp文件写实现，最好写上，否则可能报错
        cv.wait(lk, [this]
                { return !queueData.empty(); });
        // auto pt = queueData.front(); 不用这样，有个复制步骤
        std::shared_ptr<T> pt(make_shared<T>(queueData.front())); // 直接用queueData.front()
        queueData.pop();
        return pt;
    };
};