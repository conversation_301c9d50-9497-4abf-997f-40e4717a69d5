#include <iostream>
#include <vector>
#include <thread>
#include <functional>
#include <memory>
#include <list>
#include <mutex>
#include "BackgroundTask.h"

using namespace std;

// 互斥量
list<int> someList;
// 声明一个互斥锁，理解成一把钥匙，在谁谁手上，谁才能解锁对应门后的代码
mutex mutex_someList;
mutex mutex_list_a;
mutex mutex_list_b;

const int NUM_COUT = 10000;

vector<int> &f(vector<int> &vec)
{
	BackgroundTask task(vec);
	vector<int> a{2, 4, 6, 8, 10};
	// thread(函数,这里传递)的参数是拷贝方式，即使函数定义的是引用
	// 所以要ref包裹才能传递引用。
	thread t(task, ref(a));
	// 线程守护，保证主函数推出前，等待线程返回。
	ThreadGuard g(t);
	return vec;
}

void listPushSomeThing(list<int> &addList)
{
	// 声明一个叫guard的锁守护（智能结束释放），来锁叫someListMutex的互斥锁
	lock_guard<mutex> guard(mutex_someList); // 只能锁一个，c++11
	// 锁下面代码，只有一个线程能运行下面代码。
	someList.insert(someList.end(), addList.begin(), addList.end());
}

void exchangeList(list<int> &a, list<int> &b)
{
	if (&a == &b)
	{
		// 地址相同
		return;
	}
	// 同时锁多个互斥锁，c++17 新特性。作用域结束后自动解锁。
	// 人为将mutex_list_a人为是some_1的互斥锁，凡是动some_1前锁上，如添加删除节点等；mutex_list_b同理。
	scoped_lock guard(mutex_list_a, mutex_list_b);
	std::swap(a, b); // 交换a,b;
}

/*
	例如用于做数据准备，准备数据时上锁，准备完后返回锁，传递给下个步骤，
	让锁操作能在不同域中传递。
*/
unique_lock<mutex> getLock()
{
	unique_lock<mutex> lk(mutex_someList);
	for (auto &i : someList)
	{
		i = 2;
	}
	return lk;
}

/**
 * 函数间调用，避免重复上锁，用锁传递
 */
void process_someList()
{
	// 不同域中互斥量的传递
	cout << "\n锁区域传递，someList变成2：\n";
	unique_lock<mutex> lk(getLock()); // 接收锁，让其自动管理在这个函数结束时，自动释放。
	for (auto i : someList)
	{
		cout << i << " ";
	}
}

/**
 * 开开关关的颗粒度锁操作，某些操作需要上锁，某些操作不需要上锁。
 */
void process_longTime()
{
	cout << "\n颗粒度锁操作,先读当前someList的值，前面加个a：\n";
	unique_lock<mutex> lk(mutex_someList);
	for (auto i : someList)
	{
		cout << 'a' << i << " ";
	}
	lk.unlock();
	// 做其他内容，不需要锁上，能让其他线程用。
	cout << "\n准备8 list...\n";
	list<int> list_c(NUM_COUT, 8);
	cout << "\n拼接8后的someList:\n";
	lk.lock();
	someList.insert(someList.end(), list_c.begin(), list_c.end());
	for (auto i : someList)
	{
		cout << i << " ";
	}
} // 作用域结束后，会自动解锁 lk

void swich_1()
{
	vector<int> vec = {1, 2, 4, 5, 6};
	f(vec);
	cout << "\n拷贝传值示例：\n";
	for (int i = 0; i < vec.size(); i++)
	{
		cout << vec[i] << " ";
	}
	cout << endl;
	for (auto b : vec)
	{
		cout << b << " ";
	}

	cout << "\n移动传值示例：\n";
	// 使用 make_unique 创建动态分配的对象
	unique_ptr<BackgroundTask> p = make_unique<BackgroundTask>(vec);
	// unique_ptr 只能用move,不能拷贝
	thread t(&BackgroundTask::do_lengthy_work, move(p), 10);
	t.join(); // 等待线程返回。
}

void switch_2()
{
	// 测试锁，如果不锁，会出现数据错乱。
	list<int> some_1(NUM_COUT, 1);
	list<int> some_2(NUM_COUT, 9);
	thread doList_1(listPushSomeThing, ref(some_1));
	thread doList_2(listPushSomeThing, ref(some_2));
	doList_1.join();
	doList_2.join();
	cout << "\nsomeList:\n";
	for (auto i : someList)
	{
		cout << i << " ";
	}

	// 同时上锁多个互斥锁示例
	thread t_2(exchangeList, ref(some_1), ref(some_2));
	t_2.join();
	cout << "\nsome_1:\n";
	for (auto i : some_1)
	{
		cout << i << " ";
	}
	cout << "\nsome_2:\n";
	for (auto i : some_2)
	{
		cout << i << " ";
	}
}

void switch_3()
{
	// 函数转跳间的接着锁
	thread t_3(process_someList);
	t_3.detach();

	// 开开关关的颗粒度锁操作，某些操作需要上锁，某些操作不需要上锁。
	thread t_4(process_longTime);
	t_4.detach();
}

void switch_4()
{
	// 只锁一次
	OnceInitDemo demo;
	vector<int> vec1 = {6, 6, 6, 6};
	vector<int> vec2 = {7, 7, 7, 7};
	thread t_o1(&OnceInitDemo::orderSend, &demo, vec1, 'b');
	thread t_o2(&OnceInitDemo::orderSend, &demo, vec2, 'c'); // 两个线程同时调用，会互相等待，因为demo_1,demo_o2的互斥量是同一个。
	t_o1.detach();
	t_o2.detach();
	system("pause");
}
void switch_5()
{
	// 写锁，读不锁
	vector<int> dns_vec(1000, 4);
	Dns_cache dns_demo(dns_vec);
	thread t_dns_1(&Dns_cache::reader, &dns_demo, 'g');
	thread t_dns_2(&Dns_cache::reader, &dns_demo, 'k');
	thread t_dns_3(&Dns_cache::writer, &dns_demo);
	thread t_dns_4(&Dns_cache::reader, &dns_demo, 'f');
	t_dns_1.join();
	t_dns_2.join();
	t_dns_3.join();
	t_dns_4.join();
}

void switch_6(){
	// 线程安全队列

}

int main()
{
	while (true)
	{
		system("cls");
		cout << "1.拷贝/移动传值示例" << endl;
		cout << "2.锁示例，同时锁" << endl;
		cout << "3.函数转跳间的接着锁。颗粒度锁" << endl;
		cout << "4.只锁一次" << endl;
		cout << "5.写锁，读不锁" << endl;
		cout << "6.线程安全队列" << endl;
		cout << "0.exit" << endl;

		int inputValue;
		cin >> inputValue;
		switch (inputValue)
		{
		case 0:
			return 0;
		case 1:
			swich_1();
			break;
		case 2:
			switch_2();
			break;
		case 3:
			switch_3();
			break;
		case 4:
			switch_4();
			break;
		case 5:
			switch_5();
			break;
		case 6:
			switch_6();
			break;
		}

		system("pause");
	}
}